## Day 1
- Did Initial Setup
- Learnt The Basics of Next.js
- Integrated Clerk with Next.js
- <PERSON>rnt the Basics of Clerk

## Day 2
- Installed Convex
- Set up Convex Client Provider
- Created Schema by Adding Tables

## Day 3
- Added Footer in Layout, made footer component

.......

## Day 7
- Added AI Component
- AI Component is able to stream data
- AI can take the context of the code.

## Day 8
- Formatted AI Response.
- Improved AI System Prompt

## Day 9
-  Add reset button which will reset the code to default
-  Make Footer in Sidebar Responsive.
-  Remove mock files and filename

## Day 10
-  remove search form from sidebar
-  edit inbox in sidebar
-  make sidebar icons small

# Todo
- ```Important``` Add ability to provide input to the stream.
- ```Important``` Add functionality to Ask AI Button
- Convert file system to checkpoint
- Change name to ForgeIDE ( Optional )
- Add Button to get Time Complexity.

- ✅ Add reset button which will reset the code to default
- ✅ Make Footer in Sidebar Responsive.
- ✅ Remove mock files and filename
- ✅ remove search form from sidebar
- ✅ edit inbox in sidebar
- ✅ make sidebar icons small
- User might face difficulty finding where the language change options are.
