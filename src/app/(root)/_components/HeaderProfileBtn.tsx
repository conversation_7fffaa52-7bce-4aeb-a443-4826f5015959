"use client";
import { SignedOut, User<PERSON><PERSON><PERSON>, SignInButton } from "@clerk/nextjs";
import { User } from "lucide-react";
import { dark, neobrutalism } from '@clerk/themes'

function HeaderProfileBtn() {
    return (
        <>
            <UserButton
            appearance={{
                baseTheme: neobrutalism,
            }}
            >
                <UserButton.MenuItems>
                    {/* <UserButton.Link
                        label="Profile"
                        labelIcon={<User className="size-4" />}
                        href="/profile"
                    /> */}
                </UserButton.MenuItems>
            </UserButton>

            <SignedOut>
                <SignInButton mode="modal">
                    <button className="bg-white rounded hover:bg-gray-100 scale-90 text-black py-2 px-4 ">
                        Sign In
                    </button>
                </SignInButton>
            </SignedOut>
        </>
    );
}
export default HeaderProfileBtn;