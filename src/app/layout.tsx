import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON>eist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { Clerk<PERSON>rov<PERSON> } from "@clerk/nextjs";
import ConvexClientProvider from "@/components/providers/convexClientProvider";
// Unused imports removed
// import Footer from "@/components/Footer";
// import Navbar from "@/app/(root)/_components/Header";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Forge",
  description: "Forge - Share and run code snippets",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="en" className="dark">
        <body
          className={`${geistSans.variable} ${geist<PERSON>ono.variable} antialiased min-h-screen flex flex-col`}
        >
          <ConvexClientProvider>{children}</ConvexClientProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
