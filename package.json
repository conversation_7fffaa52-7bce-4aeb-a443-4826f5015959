{"name": "forge", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@clerk/clerk-react": "^5.31.4", "@clerk/nextjs": "^6.12.12", "@google/generative-ai": "^0.24.1", "@monaco-editor/react": "^4.7.0", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.4", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-dropdown-menu": "^2.1.7", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "convex": "^1.21.0", "framer-motion": "^12.7.4", "json-server": "^1.0.0-beta.3", "ldrs": "^1.1.6", "lucide-react": "^0.488.0", "monaco-editor": "^0.52.2", "next": "15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "react-loading-dots": "^0.1.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-syntax-highlighter": "^15.6.1", "remark-gfm": "^4.0.1", "svix": "^1.63.1", "tailwind-merge": "^3.2.0", "tailwind-scrollbar": "^4.0.2", "tw-animate-css": "^1.2.5", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "tailwindcss": "^4", "typescript": "^5"}}